/**
 * 地图数据API服务模块
 * Map Data API Service Module
 * 
 * 提供动态数据源集成功能，支持从服务器端API获取地理位置数据
 * Provides dynamic data source integration functionality, supports fetching geographic location data from server-side API
 * 
 * <AUTHOR> (Agricultural Development Project Team)
 * @version 1.0.0
 */

/**
 * API响应数据结构定义
 * API Response Data Structure Definition
 */

/**
 * 地理坐标点数据结构
 * Geographic coordinate point data structure
 * @typedef {Object} LocationPoint
 * @property {string} id - 位置唯一标识符 (Unique location identifier)
 * @property {string} name - 位置名称 (Location name)
 * @property {string} nameEn - 英文名称 (English name)
 * @property {number} latitude - 纬度坐标 (Latitude coordinate)
 * @property {number} longitude - 经度坐标 (Longitude coordinate)
 * @property {string} type - 位置类型 (Location type: 'city', 'port', 'border', 'hub')
 * @property {string} country - 所属国家 (Country)
 * @property {string} province - 所属省份/州 (Province/State)
 * @property {Object} metadata - 扩展元数据 (Extended metadata)
 */

/**
 * 路径连接数据结构
 * Path connection data structure
 * @typedef {Object} RouteConnection
 * @property {string} id - 路径唯一标识符 (Unique route identifier)
 * @property {string} sourceId - 起点位置ID (Source location ID)
 * @property {string} targetId - 终点位置ID (Target location ID)
 * @property {Array<Array<number>>} coords - 坐标数组 [[lat, lng], [lat, lng]] (Coordinate array)
 * @property {number} value - 数据流量值 (Data flow value)
 * @property {string} name - 路径名称 (Route name)
 * @property {string} type - 路径类型 (Route type: 'trade', 'transport', 'data', 'logistics')
 * @property {Object} metadata - 路径元数据 (Route metadata)
 * @property {number} metadata.distance - 距离(公里) (Distance in kilometers)
 * @property {string} metadata.transportMode - 运输方式 (Transport mode)
 * @property {number} metadata.frequency - 频率 (Frequency)
 */

/**
 * API响应数据结构
 * API Response data structure
 * @typedef {Object} ApiResponse
 * @property {boolean} success - 请求是否成功 (Request success status)
 * @property {string} message - 响应消息 (Response message)
 * @property {Object} data - 响应数据 (Response data)
 * @property {Array<LocationPoint>} data.locations - 位置点数据 (Location points data)
 * @property {Array<RouteConnection>} data.routes - 路径连接数据 (Route connections data)
 * @property {Object} data.metadata - 数据集元信息 (Dataset metadata)
 */

/**
 * 地图数据API服务类
 * Map Data API Service Class
 */
class MapDataApiService {
  constructor() {
    // API基础配置 (API base configuration)
    this.baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000/api'
    this.timeout = 10000 // 10秒超时 (10 seconds timeout)
    
    // 请求头配置 (Request headers configuration)
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }
  }

  /**
   * 通用HTTP请求方法
   * Generic HTTP request method
   * @param {string} endpoint - API端点 (API endpoint)
   * @param {Object} options - 请求选项 (Request options)
   * @returns {Promise<ApiResponse>} API响应 (API response)
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`
    const config = {
      method: 'GET',
      headers: { ...this.defaultHeaders, ...options.headers },
      ...options
    }

    try {
      // 添加超时控制 (Add timeout control)
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)
      
      config.signal = controller.signal

      const response = await fetch(url, config)
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('API请求失败 (API request failed):', error)
      throw new Error(`API请求失败: ${error.message}`)
    }
  }

  /**
   * 获取云南-老挝路径数据
   * Fetch Yunnan-Laos route data
   * @param {Object} params - 查询参数 (Query parameters)
   * @param {string} params.region - 区域过滤 (Region filter)
   * @param {string} params.type - 路径类型过滤 (Route type filter)
   * @returns {Promise<ApiResponse>} 路径数据响应 (Route data response)
   */
  async getYunnanLaosRoutes(params = {}) {
    const queryParams = new URLSearchParams({
      region: 'yunnan-laos',
      ...params
    })
    
    return this.request(`/routes/yunnan-laos?${queryParams}`)
  }

  /**
   * 获取区域位置点数据
   * Fetch regional location points data
   * @param {Object} params - 查询参数 (Query parameters)
   * @param {Array<number>} params.bounds - 地理边界 [minLat, minLng, maxLat, maxLng] (Geographic bounds)
   * @param {string} params.type - 位置类型过滤 (Location type filter)
   * @returns {Promise<ApiResponse>} 位置数据响应 (Location data response)
   */
  async getRegionalLocations(params = {}) {
    const queryParams = new URLSearchParams(params)
    return this.request(`/locations/regional?${queryParams}`)
  }

  /**
   * 获取实时数据流
   * Fetch real-time data flow
   * @param {string} routeId - 路径ID (Route ID)
   * @returns {Promise<ApiResponse>} 实时数据响应 (Real-time data response)
   */
  async getRealTimeFlow(routeId) {
    return this.request(`/routes/${routeId}/realtime`)
  }

  /**
   * 批量获取路径数据
   * Batch fetch route data
   * @param {Array<string>} routeIds - 路径ID数组 (Array of route IDs)
   * @returns {Promise<ApiResponse>} 批量路径数据响应 (Batch route data response)
   */
  async getBatchRoutes(routeIds) {
    return this.request('/routes/batch', {
      method: 'POST',
      body: JSON.stringify({ routeIds })
    })
  }
}

/**
 * 模拟API数据生成器
 * Mock API Data Generator
 * 用于开发和测试阶段，模拟真实的API响应数据
 * Used for development and testing phases, simulates real API response data
 */
class MockApiDataGenerator {
  /**
   * 生成云南-老挝模拟路径数据
   * Generate Yunnan-Laos mock route data
   * @returns {Promise<ApiResponse>} 模拟API响应 (Mock API response)
   */
  static async generateYunnanLaosRoutes() {
    // 模拟网络延迟 (Simulate network delay)
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))

    return {
      success: true,
      message: '云南-老挝路径数据获取成功 (Yunnan-Laos route data fetched successfully)',
      data: {
        locations: [
          // 云南省主要城市 (Major cities in Yunnan Province)
          {
            id: 'kunming',
            name: '昆明',
            nameEn: 'Kunming',
            latitude: 25.0389,
            longitude: 102.7183,
            type: 'city',
            country: 'China',
            province: 'Yunnan',
            metadata: { population: 8460000, isCapital: true }
          },
          {
            id: 'dali',
            name: '大理',
            nameEn: 'Dali',
            latitude: 25.6066,
            longitude: 100.2672,
            type: 'city',
            country: 'China',
            province: 'Yunnan',
            metadata: { population: 3500000, isCapital: false }
          },
          {
            id: 'lijiang',
            name: '丽江',
            nameEn: 'Lijiang',
            latitude: 26.8721,
            longitude: 100.2240,
            type: 'city',
            country: 'China',
            province: 'Yunnan',
            metadata: { population: 1250000, isCapital: false }
          },
          {
            id: 'xishuangbanna',
            name: '西双版纳',
            nameEn: 'Xishuangbanna',
            latitude: 22.0017,
            longitude: 100.7975,
            type: 'city',
            country: 'China',
            province: 'Yunnan',
            metadata: { population: 1300000, isCapital: false }
          },
          {
            id: 'baoshan',
            name: '保山',
            nameEn: 'Baoshan',
            latitude: 25.1204,
            longitude: 99.1670,
            type: 'city',
            country: 'China',
            province: 'Yunnan',
            metadata: { population: 2600000, isCapital: false }
          },
          
          // 老挝主要城市 (Major cities in Laos)
          {
            id: 'vientiane',
            name: '万象',
            nameEn: 'Vientiane',
            latitude: 17.9757,
            longitude: 102.6331,
            type: 'city',
            country: 'Laos',
            province: 'Vientiane',
            metadata: { population: 948477, isCapital: true }
          },
          {
            id: 'luang_prabang',
            name: '琅勃拉邦',
            nameEn: 'Luang Prabang',
            latitude: 19.8845,
            longitude: 102.1348,
            type: 'city',
            country: 'Laos',
            province: 'Luang Prabang',
            metadata: { population: 66781, isCapital: false }
          },
          {
            id: 'pakse',
            name: '巴色',
            nameEn: 'Pakse',
            latitude: 15.1202,
            longitude: 105.7985,
            type: 'city',
            country: 'Laos',
            province: 'Champasak',
            metadata: { population: 87000, isCapital: false }
          },
          {
            id: 'savannakhet',
            name: '沙湾拿吉',
            nameEn: 'Savannakhet',
            latitude: 16.5563,
            longitude: 104.7573,
            type: 'city',
            country: 'Laos',
            province: 'Savannakhet',
            metadata: { population: 125760, isCapital: false }
          }
        ],
        routes: [
          // 昆明到万象的主要贸易路线 (Main trade route from Kunming to Vientiane)
          {
            id: 'kunming-vientiane',
            sourceId: 'kunming',
            targetId: 'vientiane',
            coords: [[25.0389, 102.7183], [17.9757, 102.6331]],
            value: 280,
            name: '昆明-万象贸易走廊 (Kunming-Vientiane Trade Corridor)',
            type: 'trade',
            metadata: {
              distance: 785,
              transportMode: 'railway',
              frequency: 'daily'
            }
          },
          // 西双版纳到琅勃拉邦的旅游路线 (Tourism route from Xishuangbanna to Luang Prabang)
          {
            id: 'xishuangbanna-luang_prabang',
            sourceId: 'xishuangbanna',
            targetId: 'luang_prabang',
            coords: [[22.0017, 100.7975], [19.8845, 102.1348]],
            value: 150,
            name: '西双版纳-琅勃拉邦旅游线 (Xishuangbanna-Luang Prabang Tourism Route)',
            type: 'transport',
            metadata: {
              distance: 420,
              transportMode: 'highway',
              frequency: 'weekly'
            }
          },
          // 大理到万象的物流路线 (Logistics route from Dali to Vientiane)
          {
            id: 'dali-vientiane',
            sourceId: 'dali',
            targetId: 'vientiane',
            coords: [[25.6066, 100.2672], [17.9757, 102.6331]],
            value: 200,
            name: '大理-万象物流通道 (Dali-Vientiane Logistics Corridor)',
            type: 'logistics',
            metadata: {
              distance: 920,
              transportMode: 'highway',
              frequency: 'bi-weekly'
            }
          },
          // 保山到沙湾拿吉的边境贸易 (Border trade from Baoshan to Savannakhet)
          {
            id: 'baoshan-savannakhet',
            sourceId: 'baoshan',
            targetId: 'savannakhet',
            coords: [[25.1204, 99.1670], [16.5563, 104.7573]],
            value: 120,
            name: '保山-沙湾拿吉边贸线 (Baoshan-Savannakhet Border Trade Route)',
            type: 'trade',
            metadata: {
              distance: 1150,
              transportMode: 'highway',
              frequency: 'monthly'
            }
          },
          // 丽江到巴色的文化交流路线 (Cultural exchange route from Lijiang to Pakse)
          {
            id: 'lijiang-pakse',
            sourceId: 'lijiang',
            targetId: 'pakse',
            coords: [[26.8721, 100.2240], [15.1202, 105.7985]],
            value: 90,
            name: '丽江-巴色文化交流线 (Lijiang-Pakse Cultural Exchange Route)',
            type: 'data',
            metadata: {
              distance: 1380,
              transportMode: 'air',
              frequency: 'quarterly'
            }
          }
        ],
        metadata: {
          region: 'yunnan-laos',
          dataSource: 'mock-api',
          lastUpdated: new Date().toISOString(),
          totalRoutes: 5,
          totalLocations: 9,
          coordinateSystem: 'WGS84',
          projection: 'EPSG:4326'
        }
      }
    }
  }

  /**
   * 生成实时数据流模拟数据
   * Generate real-time data flow mock data
   * @param {string} routeId - 路径ID (Route ID)
   * @returns {Promise<ApiResponse>} 实时数据响应 (Real-time data response)
   */
  static async generateRealTimeFlow(routeId) {
    await new Promise(resolve => setTimeout(resolve, 200))

    return {
      success: true,
      message: '实时数据流获取成功 (Real-time data flow fetched successfully)',
      data: {
        routeId,
        timestamp: new Date().toISOString(),
        flowValue: Math.floor(Math.random() * 300) + 50,
        status: 'active',
        metadata: {
          updateInterval: 5000, // 5秒更新间隔 (5 seconds update interval)
          dataQuality: 'high'
        }
      }
    }
  }
}

// 创建API服务实例 (Create API service instance)
const mapDataApi = new MapDataApiService()

// 导出API服务和模拟数据生成器 (Export API service and mock data generator)
export { mapDataApi, MockApiDataGenerator }
export default mapDataApi
