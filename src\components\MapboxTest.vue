<template>
  <div class="mapbox-test">
    <h2>Mapbox GL JS 测试 (Mapbox GL JS Test)</h2>
    <div class="test-info">
      <p>状态 (Status): {{ status }}</p>
      <p>错误 (Error): {{ error }}</p>
      <p>地图已加载 (Map Loaded): {{ mapLoaded }}</p>
    </div>
    <div id="test-map" class="test-map-container"></div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl'

export default {
  name: 'MapboxTest',
  data() {
    return {
      map: null,
      status: '初始化中 (Initializing)',
      error: null,
      mapLoaded: false,
      mapboxAccessToken: 'pk.eyJ1IjoiYmFyNXhjIiwiYSI6ImNtYXdhdzcwZDBiajUydHIycjh0OXZrYW8ifQ.1pfjx8FkKHbR4n94jINJNw',
      mapboxStyleUrl: 'mapbox://styles/bar5xc/cmbou6n4400ny01s65spx6eky'
    }
  },
  mounted() {
    this.initTestMap()
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove()
    }
  },
  methods: {
    initTestMap() {
      try {
        this.status = '设置访问令牌 (Setting access token)'
        mapboxgl.accessToken = this.mapboxAccessToken

        this.status = '创建地图实例 (Creating map instance)'
        this.map = new mapboxgl.Map({
          container: 'test-map',
          style: this.mapboxStyleUrl,
          center: [101.5, 21.5], // 云南-老挝区域中心 (Yunnan-Laos region center)
          zoom: 6
        })

        this.status = '等待地图加载 (Waiting for map to load)'

        this.map.on('load', () => {
          this.status = '地图加载成功 (Map loaded successfully)'
          this.mapLoaded = true
          console.log('测试地图加载成功 (Test map loaded successfully)')
        })

        this.map.on('error', (e) => {
          this.status = '地图加载失败 (Map load failed)'
          this.error = e.error ? e.error.message : '未知错误 (Unknown error)'
          console.error('测试地图加载错误 (Test map load error):', e)
        })

      } catch (error) {
        this.status = '初始化失败 (Initialization failed)'
        this.error = error.message
        console.error('测试地图初始化错误 (Test map initialization error):', error)
      }
    }
  }
}
</script>

<style scoped>
.mapbox-test {
  padding: 20px;
  background-color: #0F2E2C;
  color: #00FFCC;
  font-family: 'Microsoft YaHei', sans-serif;
}

.test-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(0, 255, 204, 0.1);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 8px;
}

.test-info p {
  margin: 5px 0;
}

.test-map-container {
  width: 100%;
  height: 400px;
  border: 2px solid rgba(0, 255, 204, 0.3);
  border-radius: 8px;
  background-color: #122e2c;
}
</style>
