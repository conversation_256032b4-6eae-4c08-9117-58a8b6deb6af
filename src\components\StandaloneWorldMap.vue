<template>
  <div class="standalone-world-map" :style="containerStyle">
    <div class="map-title" v-if="showTitle">
      <span class="title-text">{{ title }}</span>
    </div>
    <div :id="mapId" class="map-container" :class="{ 'show-grid': showGrid }" :style="mapContainerStyle"></div>
  </div>
</template>

<script>
import mapboxgl from 'mapbox-gl'

export default {
  name: 'StandaloneWorldMap',
  props: {
    // Map configuration
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '500px'
    },
    title: {
      type: String,
      default: 'Global Distribution'
    },
    showTitle: {
      type: Boolean,
      default: true
    },

    // Map behavior
    center: {
      type: Array,
      default: () => [20, 0]
    },
    zoom: {
      type: Number,
      default: 2
    },
    // 区域模式配置 (Regional mode configuration)
    regionalMode: {
      type: <PERSON>olean,
      default: false
    },
    // 区域配置对象 (Regional configuration object)
    regionConfig: {
      type: Object,
      default: () => ({
        name: 'global',
        center: [20, 0],
        zoom: 2,
        bounds: null
      })
    },

    // Path data
    pathData: {
      type: Array,
      default: () => []
    },

    // Styling options
    colors: {
      type: Object,
      default: () => ({
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#1ED9B5',
        sourcePoint: '#1ED9B5',
        targetPoint: '#1ED9B5'
      })
    },

    // Animation settings
    animationEnabled: {
      type: Boolean,
      default: true
    },
    animationSpeed: {
      type: Number,
      default: 2000
    },
    flowAnimationSpeed: {
      type: Number,
      default: 0.002, // Speed of flow particles along paths (further reduced for slower, more deliberate animation)
      validator: (value) => value > 0 && value <= 0.1
    },
    flowParticleCount: {
      type: Number,
      default: 1, // Number of flow particles per path (single particle for cleaner effect)
      validator: (value) => value >= 1 && value <= 10
    },
    // 流星尾迹效果设置 (Meteor trail effect settings)
    trailLength: {
      type: Number,
      default: 0.25, // Trail length as percentage of total path (25% for optimal visual effect)
      validator: (value) => value > 0 && value <= 1
    },
    trailSegments: {
      type: Number,
      default: 20, // Number of segments in the trail for smooth gradient effect
      validator: (value) => value >= 5 && value <= 50
    },

    // Grid overlay
    showGrid: {
      type: Boolean,
      default: true
    },

    // 最小化模式 - 隐藏文本标签和边界 (Minimalist mode - hide text labels and borders)
    minimalistMode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      flowAnimations: [], // Store flow animation data
      mapboxLoaded: false,
      mapId: `world-map-${Math.random().toString(36).substring(2, 11)}`,
      // Mapbox GL JS 配置 (Mapbox GL JS configuration)
      mapboxAccessToken: 'pk.eyJ1IjoiYmFyNXhjIiwiYSI6ImNtYXdhdzcwZDBiajUydHIycjh0OXZrYW8ifQ.1pfjx8FkKHbR4n94jINJNw',
      mapboxStyleUrl: 'mapbox://styles/bar5xc/cmbou6n4400ny01s65spx6eky',
      // 动画和图层管理 (Animation and layer management)
      animationFrameIds: [],
      pathSources: new Map(),
      markerElements: new Map()
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    mapContainerStyle() {
      return {
        backgroundColor: this.colors.ocean
      }
    },
    // 计算实际使用的地图中心点 (Calculate actual map center to use)
    // Mapbox uses [longitude, latitude] format
    actualMapCenter() {
      const center = this.regionalMode && this.regionConfig.center
        ? this.regionConfig.center
        : this.center
      // Convert from [lat, lng] to [lng, lat] for Mapbox
      return [center[1], center[0]]
    },
    // 计算实际使用的缩放级别 (Calculate actual zoom level to use)
    actualMapZoom() {
      return this.regionalMode && this.regionConfig.zoom
        ? this.regionConfig.zoom
        : this.zoom
    }
  },
  mounted() {
    this.initMapbox()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    pathData: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          this.renderPaths()
        }
      },
      deep: true
    },
    animationEnabled: {
      handler(newValue) {
        if (!newValue) {
          // Stop all flow animations when animation is disabled
          this.flowAnimations.forEach(animationData => {
            if (animationData.animationFrameId) {
              cancelAnimationFrame(animationData.animationFrameId)
              animationData.animationFrameId = null
            }
            animationData.isActive = false
          })
        } else if (this.map && this.mapboxLoaded) {
          // Restart animations when re-enabled
          this.renderPaths()
        }
      }
    },
    // 监听区域模式变化 (Watch for regional mode changes)
    regionalMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          // 重新设置地图视图 (Reset map view)
          this.updateMapView()
        }
      }
    },

    // 监听简洁模式变化 (Watch for minimalist mode changes)
    minimalistMode: {
      handler() {
        if (this.map && this.mapboxLoaded) {
          console.log('简洁模式切换 (Minimalist mode toggled):', this.minimalistMode)
          // 重新渲染路径 (Re-render paths)
          this.renderPaths()
        }
      }
    },
    // 监听区域配置变化 (Watch for region config changes)
    regionConfig: {
      handler() {
        if (this.map && this.mapboxLoaded && this.regionalMode) {
          // 更新地图视图 (Update map view)
          this.updateMapView()
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 初始化Mapbox GL JS地图 (Initialize Mapbox GL JS map)
     */
    initMapbox() {
      console.log('开始初始化Mapbox地图 (Starting Mapbox map initialization)')
      console.log('访问令牌 (Access token):', this.mapboxAccessToken.substring(0, 20) + '...')
      console.log('样式URL (Style URL):', this.mapboxStyleUrl)
      console.log('地图中心点 (Map center):', this.actualMapCenter)
      console.log('缩放级别 (Zoom level):', this.actualMapZoom)

      // 设置Mapbox访问令牌 (Set Mapbox access token)
      mapboxgl.accessToken = this.mapboxAccessToken

      // 确保Mapbox CSS已加载 (Ensure Mapbox CSS is loaded)
      this.loadMapboxCSS()

      try {
        // 创建地图实例 - 让Mapbox样式处理默认视图设置 (Create map instance - let Mapbox style handle default view settings)
        this.map = new mapboxgl.Map({
          container: this.mapId,
          style: this.mapboxStyleUrl,
          // 不设置center和zoom，让样式配置处理 (Don't set center and zoom, let style configuration handle it)
          interactive: false, // 禁用交互以匹配原始行为 (Disable interaction to match original behavior)
          attributionControl: false,
          logoPosition: 'bottom-right'
        })
        console.log('Mapbox地图实例创建成功 (Mapbox map instance created successfully)')
      } catch (error) {
        console.error('创建Mapbox地图实例失败 (Failed to create Mapbox map instance):', error)
        return
      }

      // 地图加载完成后的处理 (Handle map load completion)
      this.map.on('load', () => {
        console.log('Mapbox地图加载完成 (Mapbox map loaded)')
        this.mapboxLoaded = true

        // 记录当前地图视图信息 (Log current map view info)
        console.log('地图当前中心点 (Current map center):', this.map.getCenter())
        console.log('地图当前缩放级别 (Current map zoom):', this.map.getZoom())

        // 只在明确需要时设置边界（区域模式且有边界配置）(Only set bounds when explicitly needed)
        if (this.regionalMode && this.regionConfig.bounds) {
          this.applyRegionalBounds()
        }

        // 渲染路径数据 (Render path data)
        if (this.pathData.length > 0) {
          this.renderPaths()
        }

        // 发出地图就绪事件 (Emit map ready event)
        this.$emit('map-ready', this.map)
      })

      // 错误处理 (Error handling)
      this.map.on('error', (e) => {
        console.error('Mapbox地图加载错误 (Mapbox map load error):', e)
        console.error('错误详情 (Error details):', {
          error: e.error,
          sourceId: e.sourceId,
          isSourceLoaded: e.isSourceLoaded
        })
      })

      // 样式加载事件 (Style load events)
      this.map.on('styledata', () => {
        console.log('Mapbox样式数据加载完成 (Mapbox style data loaded)')
      })

      this.map.on('sourcedataloading', (e) => {
        console.log('Mapbox数据源开始加载 (Mapbox source data loading):', e.sourceId)
      })

      this.map.on('sourcedata', (e) => {
        console.log('Mapbox数据源加载完成 (Mapbox source data loaded):', e.sourceId)
      })

      // 添加点击事件用于调试 (Add click event for debugging)
      this.map.on('click', (e) => {
        console.log('地图点击位置 (Map click position):', e.lngLat)
      })
    },

    /**
     * 加载Mapbox CSS样式 (Load Mapbox CSS styles)
     */
    loadMapboxCSS() {
      if (!document.querySelector('link[href*="mapbox-gl.css"]')) {
        const mapboxCss = document.createElement('link')
        mapboxCss.rel = 'stylesheet'
        mapboxCss.href = 'https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css'
        document.head.appendChild(mapboxCss)
      }
    },

    /**
     * 渲染路径数据到Mapbox地图 (Render path data to Mapbox map)
     */
    renderPaths() {
      if (!this.map || !this.mapboxLoaded) return

      console.log('开始渲染路径 (Starting to render paths):', this.pathData.length)

      // 清除现有的路径和动画 (Clear existing paths and animations)
      this.clearMapLayers()

      // 为每条路径创建数据源和图层 (Create data sources and layers for each path)
      this.pathData.forEach((path, index) => {
        this.renderSinglePath(path, index)
      })
    },

    /**
     * 渲染单条路径到Mapbox地图 (Render single path to Mapbox map)
     */
    renderSinglePath(path, index) {
      if (!path.coords || path.coords.length < 2) {
        console.warn(`路径 ${index} 坐标无效 (Path ${index} has invalid coordinates):`, path)
        return
      }

      const [sourceCoords, targetCoords] = path.coords
      const pathId = `path-${index}`
      const sourceId = `source-${index}`
      const targetId = `target-${index}`

      console.log(`渲染路径 ${index} (Rendering path ${index}):`, {
        name: path.name,
        sourceCoords,
        targetCoords,
        pathId
      })

      // 生成曲线路径点 (Generate curved path points)
      const curvePoints = this.generateCurvePoints(sourceCoords, targetCoords)

      // 转换坐标格式为Mapbox格式 [lng, lat] (Convert coordinates to Mapbox format [lng, lat])
      const mapboxCurvePoints = curvePoints.map(point => [point[1], point[0]])

      console.log(`路径 ${index} 曲线点数量 (Path ${index} curve points count):`, mapboxCurvePoints.length)

      // 不再添加静态路径线，改为动态流星尾迹效果 (No longer add static path lines, use dynamic meteor trail effect instead)
      // 流星尾迹将在动画过程中动态创建和更新 (Meteor trail will be dynamically created and updated during animation)
      console.log(`跳过静态路径渲染，使用动态流星尾迹 (Skipping static path rendering, using dynamic meteor trail): ${pathId}`)

      // 创建起点和终点标记 (Create source and target markers)
      this.createMarker(sourceCoords, sourceId, 'source', path, index)
      this.createMarker(targetCoords, targetId, 'target', path, index)

      // 存储路径信息用于流星尾迹效果 (Store path information for meteor trail effect)
      this.pathSources.set(pathId, {
        sourceId: pathId,
        layerIds: [], // 动态流星尾迹不使用静态图层 (Dynamic meteor trail doesn't use static layers)
        curvePoints: curvePoints,
        pathData: path,
        trailLayers: [] // 存储动态创建的尾迹图层ID (Store dynamically created trail layer IDs)
      })

      // 如果启用动画，创建流动动画 (If animation enabled, create flow animation)
      if (this.animationEnabled) {
        this.createFlowAnimation(curvePoints, index, pathId)
      }
    },

    /**
     * 创建标记点 (Create marker)
     */
    createMarker(coords, markerId, type, pathData, index) {
      // 创建标记元素 (Create marker element)
      const markerElement = document.createElement('div')
      markerElement.className = `marker ${type}-marker`
      markerElement.style.cssText = `
        width: ${Math.max(8, (pathData.value || 50) / 30)}px;
        height: ${Math.max(8, (pathData.value || 50) / 30)}px;
        background-color: ${this.colors[type + 'Point']};
        border: 2px solid ${this.colors[type + 'Point']};
        border-radius: 50%;
        box-shadow: 0 0 10px ${this.colors[type + 'Point']};
        cursor: pointer;
        transition: all 0.3s ease;
      `

      // 创建Mapbox标记 (Create Mapbox marker)
      const marker = new mapboxgl.Marker(markerElement)
        .setLngLat([coords[1], coords[0]]) // Mapbox uses [lng, lat]
        .addTo(this.map)

      // 添加点击事件和提示信息 (Add click event and tooltip)
      if (pathData.name) {
        const tooltipText = type === 'source'
          ? pathData.name.split(' to ')[0] || 'Source'
          : pathData.name.split(' to ')[1] || 'Target'

        markerElement.title = tooltipText
      }

      // 存储标记引用 (Store marker reference)
      this.markerElements.set(markerId, {
        marker: marker,
        element: markerElement,
        type: type,
        coords: coords,
        pathData: pathData
      })

      // 如果启用动画，开始标记动画 (If animation enabled, start marker animation)
      if (this.animationEnabled) {
        this.animateMarker(markerElement, index + (type === 'target' ? 0.5 : 0))
      }

      return marker
    },

    /**
     * 生成曲线路径点 (Generate curved path points)
     */
    generateCurvePoints(start, end, numPoints = 20) {
      const points = []

      // Calculate the midpoint
      const midLat = (start[0] + end[0]) / 2
      const midLng = (start[1] + end[1]) / 2

      // Calculate the distance for curve height
      const distance = Math.sqrt(
        Math.pow(end[0] - start[0], 2) + Math.pow(end[1] - start[1], 2)
      )

      // Add curvature based on distance
      const curvature = distance * 0.3

      // Determine if we should curve up or down based on hemisphere
      const curveDirection = midLat > 0 ? 1 : -1
      const controlPoint = [midLat + (curvature * curveDirection), midLng]

      // Generate points along the quadratic bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints
        const lat = Math.pow(1 - t, 2) * start[0] + 2 * (1 - t) * t * controlPoint[0] + Math.pow(t, 2) * end[0]
        const lng = Math.pow(1 - t, 2) * start[1] + 2 * (1 - t) * t * controlPoint[1] + Math.pow(t, 2) * end[1]
        points.push([lat, lng])
      }

      return points
    },

    /**
     * 动画标记点 (Animate marker)
     */
    animateMarker(markerElement, index) {
      if (!markerElement || !this.animationEnabled) return

      let opacity = 0.3
      let increasing = true

      const animate = () => {
        if (increasing) {
          opacity += 0.015
          if (opacity >= 1) {
            increasing = false
          }
        } else {
          opacity -= 0.015
          if (opacity <= 0.3) {
            increasing = true
          }
        }

        // 更新标记元素的透明度和发光效果 (Update marker element opacity and glow effect)
        markerElement.style.opacity = opacity
        markerElement.style.boxShadow = `0 0 ${10 + opacity * 10}px ${markerElement.style.backgroundColor}`

        // 继续动画如果标记仍然有效且动画已启用 (Continue animation if marker is still valid and animation is enabled)
        if (markerElement.parentNode && this.animationEnabled) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }

      // 根据索引延迟开始动画 (Start animation with delay based on index)
      setTimeout(() => {
        if (this.animationEnabled && markerElement.parentNode) {
          const animationId = requestAnimationFrame(animate)
          this.animationFrameIds.push(animationId)
        }
      }, index * 150)
    },

    /**
     * 创建流动动画 (Create flow animation)
     */
    createFlowAnimation(curvePoints, pathIndex, pathId) {
      if (!this.map || !this.animationEnabled || curvePoints.length < 2) return

      // 创建多个流动粒子以获得更好的视觉效果 (Create multiple flow particles for better visual effect)
      const numParticles = this.flowParticleCount
      const particles = []

      for (let i = 0; i < numParticles; i++) {
        // 创建粒子元素 (Create particle element)
        const particleElement = document.createElement('div')
        particleElement.className = 'flow-particle'
        // 使用白色粒子与绿色路径形成对比 (Use white particles to contrast with green paths)
        const particleColor = '#FFFFFF' // 白色粒子与绿色路径形成清晰对比 (White particles create clear contrast with green paths)
        particleElement.style.cssText = `
          width: 6px;
          height: 6px;
          background-color: ${particleColor};
          border: 1px solid ${particleColor};
          border-radius: 50%;
          box-shadow: 0 0 8px ${particleColor};
          position: absolute;
          pointer-events: none;
          z-index: 1000;
        `

        // 创建Mapbox标记 (Create Mapbox marker)
        const particleMarker = new mapboxgl.Marker(particleElement)
          .setLngLat([curvePoints[0][1], curvePoints[0][0]]) // 从起点开始 (Start from beginning)
          .addTo(this.map)

        particles.push({
          marker: particleMarker,
          element: particleElement,
          progress: 0, // 所有粒子从起点开始，实现顺序流动 (All particles start from beginning for sequential flow)
          pathIndex: pathIndex,
          isActive: i === 0 // 只有第一个粒子开始时是活跃的 (Only first particle is active initially)
        })
      }

      // 动画状态 (Animation state)
      const animationData = {
        particles: particles,
        curvePoints: curvePoints,
        pathIndex: pathIndex,
        pathId: pathId,
        animationFrameId: null,
        isActive: true
      }

      this.flowAnimations.push(animationData)

      // 动画函数 (Animation function)
      const animateFlow = () => {
        if (!this.animationEnabled || !animationData.isActive) {
          return
        }

        let allParticlesValid = true

        // 实现顺序粒子流动：一次只有一个粒子活跃 (Implement sequential particle flow: only one particle active at a time)
        let activeParticleIndex = -1
        let currentActiveParticle = null

        // 找到当前活跃的粒子 (Find currently active particle)
        for (let i = 0; i < animationData.particles.length; i++) {
          if (animationData.particles[i].isActive) {
            activeParticleIndex = i
            currentActiveParticle = animationData.particles[i]
            break
          }
        }

        // 如果没有活跃粒子，激活第一个 (If no active particle, activate the first one)
        if (currentActiveParticle === null) {
          animationData.particles[0].isActive = true
          currentActiveParticle = animationData.particles[0]
          activeParticleIndex = 0
        }

        // 处理当前活跃的粒子 (Process the currently active particle)
        if (currentActiveParticle && currentActiveParticle.element.parentNode) {
          // 更新粒子进度 (Update particle progress)
          currentActiveParticle.progress += this.flowAnimationSpeed

          // 当粒子完成路径时，切换到下一个粒子 (When particle completes path, switch to next particle)
          if (currentActiveParticle.progress >= 1) {
            // 隐藏当前粒子 (Hide current particle)
            currentActiveParticle.element.style.opacity = 0
            currentActiveParticle.isActive = false
            currentActiveParticle.progress = 0

            // 清除流星尾迹 (Clear meteor trail)
            try {
              this.clearMeteorTrail(pathId)
            } catch (clearError) {
              console.warn(`完成时清理流星尾迹失败 (Failed to clear meteor trail on completion):`, clearError.message)
            }

            // 激活下一个粒子（循环到第一个）(Activate next particle, cycling back to first)
            const nextIndex = (activeParticleIndex + 1) % animationData.particles.length

            // 添加延迟后激活下一个粒子 (Add delay before activating next particle)
            setTimeout(() => {
              if (animationData.isActive && this.animationEnabled) {
                animationData.particles[nextIndex].isActive = true
                animationData.particles[nextIndex].progress = 0
              }
            }, 1000) // 1秒延迟创建间隔效果 (1 second delay creates spacing effect)
          } else {
            // 计算沿曲线的位置 (Calculate position along the curve)
            const position = this.getPositionAlongPath(curvePoints, currentActiveParticle.progress)
            if (position) {
              try {
                // 更新粒子位置 (Update particle position)
                currentActiveParticle.marker.setLngLat([position[1], position[0]]) // Mapbox uses [lng, lat]

                // 计算箭头方向角度 (Calculate arrow direction angle)
                const directionAngle = this.getDirectionAngleAlongPath(curvePoints, currentActiveParticle.progress)

                // 根据进度添加淡入淡出效果 (Add fade effect based on progress)
                const opacity = Math.sin(currentActiveParticle.progress * Math.PI) * 0.7 + 0.3
                currentActiveParticle.element.style.opacity = opacity

                // 使用白色箭头的发光效果和动态旋转 (Use white arrow glow effect and dynamic rotation)
                const particleColor = '#FFFFFF'
                currentActiveParticle.element.style.filter = `drop-shadow(0 0 ${4 * opacity}px ${particleColor})`
                currentActiveParticle.element.style.transform = `rotate(${directionAngle}deg)`

                // 创建流星尾迹效果 (Create meteor trail effect)
                try {
                  this.createMeteorTrail(curvePoints, currentActiveParticle.progress, pathId)
                } catch (trailError) {
                  console.warn(`流星尾迹创建失败 (Meteor trail creation failed):`, trailError.message)
                }
              } catch (e) {
                // 优雅地处理潜在错误 (Handle potential errors gracefully)
                console.warn('Flow animation error:', e)
                allParticlesValid = false
              }
            }
          }
        } else {
          allParticlesValid = false
        }

        // 隐藏所有非活跃粒子 (Hide all inactive particles)
        animationData.particles.forEach((particle) => {
          if (!particle.isActive && particle.element.parentNode) {
            particle.element.style.opacity = 0
          }
        })

        // 如果所有粒子都有效且动画已启用，则继续动画 (Continue animation if all particles are valid and animation is enabled)
        if (allParticlesValid && this.animationEnabled && animationData.isActive) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        } else {
          // 如果动画应该停止，则清理 (Clean up if animation should stop)
          this.cleanupFlowAnimation(animationData)
        }
      }

      // 根据路径索引延迟开始动画 (Start the animation with a delay based on path index)
      setTimeout(() => {
        if (this.animationEnabled && animationData.isActive) {
          animationData.animationFrameId = requestAnimationFrame(animateFlow)
        }
      }, pathIndex * 200)
    },

    /**
     * 获取沿路径的位置 (Get position along path)
     */
    getPositionAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return null
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算沿路径的确切位置 (Calculate the exact position along the path)
      const totalSegments = points.length - 1
      const segmentProgress = progress * totalSegments
      const segmentIndex = Math.floor(segmentProgress)
      const localProgress = segmentProgress - segmentIndex

      // 处理进度恰好为1的边缘情况 (Handle edge case where progress is exactly 1)
      if (segmentIndex >= totalSegments) {
        return points[points.length - 1]
      }

      // 在两点之间插值 (Interpolate between two points)
      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1]

      const lat = startPoint[0] + (endPoint[0] - startPoint[0]) * localProgress
      const lng = startPoint[1] + (endPoint[1] - startPoint[1]) * localProgress

      return [lat, lng]
    },

    /**
     * 计算路径上指定进度点的方向角度 (Calculate direction angle at specified progress point on path)
     */
    getDirectionAngleAlongPath(points, progress) {
      if (!points || points.length < 2 || progress < 0 || progress > 1) {
        return 0
      }

      // 将进度限制在有效范围内 (Clamp progress to valid range)
      progress = Math.max(0, Math.min(1, progress))

      // 计算当前位置和稍微前进的位置来确定方向 (Calculate current position and slightly advanced position to determine direction)
      const deltaProgress = 0.01 // 小的进度增量用于计算方向 (Small progress increment for direction calculation)
      const currentProgress = progress
      const nextProgress = Math.min(1, progress + deltaProgress)

      // 获取当前点和下一个点 (Get current point and next point)
      const currentPoint = this.getPositionAlongPath(points, currentProgress)
      const nextPoint = this.getPositionAlongPath(points, nextProgress)

      if (!currentPoint || !nextPoint) {
        return 0
      }

      // 计算方向向量 (Calculate direction vector)
      const deltaLat = nextPoint[0] - currentPoint[0]
      const deltaLng = nextPoint[1] - currentPoint[1]

      // 计算角度（以弧度为单位，然后转换为度数）(Calculate angle in radians, then convert to degrees)
      let angle = Math.atan2(deltaLng, deltaLat) * (180 / Math.PI)

      // 调整角度使箭头指向正确方向 (Adjust angle so arrow points in correct direction)
      // CSS箭头默认指向上方，需要旋转90度对齐 (CSS arrow points up by default, need 90 degree rotation to align)
      angle = angle + 90

      return angle
    },

    /**
     * 创建流星尾迹效果 (Create meteor trail effect)
     */
    createMeteorTrail(curvePoints, currentProgress, pathId) {
      if (!this.map || !curvePoints || curvePoints.length < 2) return

      // 计算尾迹范围 (Calculate trail range)
      const trailStart = Math.max(0, currentProgress - this.trailLength)
      const trailEnd = currentProgress

      if (trailStart >= trailEnd) return

      // 清除之前的尾迹图层 (Clear previous trail layers)
      this.clearMeteorTrail(pathId)

      // 确保路径信息存在 (Ensure path info exists)
      const pathInfo = this.pathSources.get(pathId)
      if (!pathInfo) {
        console.warn(`路径信息不存在 (Path info not found): ${pathId}`)
        return
      }

      // 创建尾迹段 (Create trail segments)
      const segmentLength = (trailEnd - trailStart) / this.trailSegments

      for (let i = 0; i < this.trailSegments; i++) {
        const segmentStart = trailStart + (i * segmentLength)
        const segmentEnd = trailStart + ((i + 1) * segmentLength)

        // 计算段的透明度（距离箭头越近越亮）(Calculate segment opacity - brighter closer to arrow)
        const distanceFromArrow = trailEnd - segmentEnd
        const normalizedDistance = distanceFromArrow / this.trailLength
        const opacity = Math.max(0.1, 1 - normalizedDistance) * 0.8

        // 获取段的起点和终点坐标 (Get segment start and end coordinates)
        const startPoint = this.getPositionAlongPath(curvePoints, segmentStart)
        const endPoint = this.getPositionAlongPath(curvePoints, segmentEnd)

        if (!startPoint || !endPoint) continue

        // 创建段的GeoJSON (Create segment GeoJSON)
        const segmentGeoJSON = {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: [
              [startPoint[1], startPoint[0]], // Mapbox uses [lng, lat]
              [endPoint[1], endPoint[0]]
            ]
          }
        }

        const segmentId = `${pathId}-trail-${i}`
        const segmentSourceId = `${pathId}-trail-source-${i}`

        try {
          // 添加段数据源 (Add segment data source)
          if (!this.map.getSource(segmentSourceId)) {
            this.map.addSource(segmentSourceId, {
              type: 'geojson',
              data: segmentGeoJSON
            })
            console.log(`已添加尾迹数据源 (Added trail source): ${segmentSourceId}`)
          } else {
            // 更新现有数据源 (Update existing source)
            this.map.getSource(segmentSourceId).setData(segmentGeoJSON)
          }

          // 添加段图层 (Add segment layer)
          if (!this.map.getLayer(segmentId)) {
            this.map.addLayer({
              id: segmentId,
              type: 'line',
              source: segmentSourceId,
              layout: {
                'line-join': 'round',
                'line-cap': 'round'
              },
              paint: {
                'line-color': '#FFFFFF', // 白色流星尾迹 (White meteor trail)
                'line-width': 2,
                'line-opacity': opacity
              }
            })
            console.log(`已添加尾迹图层 (Added trail layer): ${segmentId}`)

            // 添加发光效果 (Add glow effect)
            const glowId = `${segmentId}-glow`
            if (!this.map.getLayer(glowId)) {
              this.map.addLayer({
                id: glowId,
                type: 'line',
                source: segmentSourceId,
                layout: {
                  'line-join': 'round',
                  'line-cap': 'round'
                },
                paint: {
                  'line-color': '#FFFFFF',
                  'line-width': 6,
                  'line-opacity': opacity * 0.3,
                  'line-blur': 2
                }
              }, segmentId) // 将发光层放在线条层下方 (Place glow layer below line layer)
              console.log(`已添加尾迹发光图层 (Added trail glow layer): ${glowId}`)
            }

            // 存储图层ID以便后续清理 (Store layer IDs for cleanup)
            const pathInfo = this.pathSources.get(pathId)
            if (pathInfo) {
              pathInfo.trailLayers.push(segmentId, glowId)
            }
          }
        } catch (error) {
          console.error(`创建流星尾迹段失败 (Failed to create meteor trail segment) ${segmentId}:`, error.message)
        }
      }
    },

    /**
     * 清除流星尾迹 (Clear meteor trail)
     */
    clearMeteorTrail(pathId) {
      const pathInfo = this.pathSources.get(pathId)
      if (!pathInfo || !pathInfo.trailLayers || pathInfo.trailLayers.length === 0) return

      console.log(`清理流星尾迹 (Clearing meteor trail): ${pathId}, 图层数量 (layer count): ${pathInfo.trailLayers.length}`)

      // 第一步：收集所有需要清理的图层和数据源 (Step 1: Collect all layers and sources to clean)
      const layersToRemove = []
      const sourcesToRemove = new Set()

      pathInfo.trailLayers.forEach(layerId => {
        if (this.map && this.map.getLayer(layerId)) {
          layersToRemove.push(layerId)

          // 计算对应的数据源ID (Calculate corresponding source ID)
          if (!layerId.includes('-glow')) {
            // 这是主要的尾迹图层，计算其数据源ID (This is main trail layer, calculate its source ID)
            const sourceId = layerId.replace('trail-', 'trail-source-')
            sourcesToRemove.add(sourceId)
          }
        }
      })

      // 第二步：移除所有图层 (Step 2: Remove all layers first)
      layersToRemove.forEach(layerId => {
        try {
          if (this.map && this.map.getLayer(layerId)) {
            this.map.removeLayer(layerId)
            console.log(`已移除图层 (Removed layer): ${layerId}`)
          }
        } catch (error) {
          console.warn(`移除图层失败 (Failed to remove layer) ${layerId}:`, error.message)
        }
      })

      // 第三步：移除所有数据源 (Step 3: Remove all data sources after layers are gone)
      sourcesToRemove.forEach(sourceId => {
        try {
          if (this.map && this.map.getSource(sourceId)) {
            this.map.removeSource(sourceId)
            console.log(`已移除数据源 (Removed source): ${sourceId}`)
          }
        } catch (error) {
          console.warn(`移除数据源失败 (Failed to remove source) ${sourceId}:`, error.message)
        }
      })

      // 第四步：清空图层数组 (Step 4: Clear the layers array)
      pathInfo.trailLayers = []
      console.log(`流星尾迹清理完成 (Meteor trail cleanup completed): ${pathId}`)
    },

    /**
     * 清理流动动画 (Cleanup flow animation)
     */
    cleanupFlowAnimation(animationData) {
      if (!animationData) return

      // 取消动画帧 (Cancel animation frame)
      if (animationData.animationFrameId) {
        cancelAnimationFrame(animationData.animationFrameId)
        animationData.animationFrameId = null
      }

      // 从地图中移除粒子 (Remove particles from map)
      animationData.particles.forEach((particle) => {
        if (particle.marker && this.map) {
          try {
            particle.marker.remove()
          } catch (e) {
            // 在清理过程中忽略错误 (Ignore errors during cleanup)
          }
        }
      })

      // 标记为非活动状态 (Mark as inactive)
      animationData.isActive = false
    },

    /**
     * 清理地图图层 (Clear map layers)
     */
    clearMapLayers() {
      // 清理所有动画帧 (Clear all animation frames)
      this.animationFrameIds.forEach(id => {
        cancelAnimationFrame(id)
      })
      this.animationFrameIds = []

      // 清理路径数据源和图层 (Clear path data sources and layers)
      this.pathSources.forEach((pathInfo, pathId) => {
        // 移除静态图层 (Remove static layers)
        pathInfo.layerIds.forEach(layerId => {
          if (this.map.getLayer(layerId)) {
            this.map.removeLayer(layerId)
          }
        })

        // 清理流星尾迹图层 (Clear meteor trail layers)
        try {
          this.clearMeteorTrail(pathId)
        } catch (error) {
          console.error(`清理流星尾迹失败 (Failed to clear meteor trail) ${pathId}:`, error.message)
        }

        // 移除数据源 (Remove data source)
        if (this.map.getSource(pathId)) {
          this.map.removeSource(pathId)
        }
      })
      this.pathSources.clear()

      // 清理标记 (Clear markers)
      this.markerElements.forEach((markerInfo) => {
        if (markerInfo.marker) {
          markerInfo.marker.remove()
        }
      })
      this.markerElements.clear()

      // 清理流动动画 (Clear flow animations)
      this.flowAnimations.forEach(animationData => {
        this.cleanupFlowAnimation(animationData)
      })
      this.flowAnimations = []

      // 清理旧的数组（向后兼容）(Clear old arrays for backward compatibility)
      this.pathLines = []
      this.sourceMarkers = []
      this.targetMarkers = []
    },

    /**
     * 组件清理 (Component cleanup)
     */
    cleanup() {
      // 清理所有图层 (Clear all layers)
      this.clearMapLayers()

      // 移除地图 (Remove map)
      if (this.map) {
        this.map.remove()
        this.map = null
      }

      // 重置状态 (Reset state)
      this.mapboxLoaded = false
    },

    /**
     * 应用区域边界限制 (Apply regional bounds restrictions)
     */
    applyRegionalBounds() {
      if (!this.map || !this.mapboxLoaded || !this.regionConfig.bounds) return

      try {
        const bounds = this.regionConfig.bounds
        const mapboxBounds = [
          [bounds.west, bounds.south], // 西南角 (Southwest corner)
          [bounds.east, bounds.north]  // 东北角 (Northeast corner)
        ]
        this.map.setMaxBounds(mapboxBounds)
        console.log('区域边界已应用 (Regional bounds applied):', bounds)
      } catch (error) {
        console.error('应用区域边界失败 (Failed to apply regional bounds):', error)
      }
    },

    /**
     * 更新地图视图 - 仅在模式切换时使用 (Update map view - only used when switching modes)
     */
    updateMapView() {
      if (!this.map || !this.mapboxLoaded) return

      try {
        // 只在明确切换模式时才改变视图 (Only change view when explicitly switching modes)
        if (this.regionalMode && this.regionConfig.center && this.regionConfig.zoom) {
          // 切换到区域模式 (Switch to regional mode)
          this.map.flyTo({
            center: this.actualMapCenter,
            zoom: this.actualMapZoom,
            duration: 1000 // 1秒动画过渡 (1 second animation transition)
          })

          // 应用区域边界 (Apply regional bounds)
          this.applyRegionalBounds()

          console.log(`切换到区域模式 (Switched to regional mode): 中心点(Center) ${this.actualMapCenter}, 缩放级别(Zoom) ${this.actualMapZoom}`)
        } else if (!this.regionalMode) {
          // 切换到全球模式 - 清除边界限制 (Switch to global mode - clear boundary restrictions)
          this.map.setMaxBounds(null)

          // 可以选择飞回到全球视图或保持当前视图 (Can choose to fly back to global view or keep current view)
          // 这里我们保持当前视图，让用户手动调整 (Here we keep current view, let user adjust manually)
          console.log('切换到全球模式 (Switched to global mode)')
        }
      } catch (error) {
        console.error('更新地图视图失败 (Failed to update map view):', error)
      }
    },

    // 公共方法用于外部控制 (Public methods for external control)
    addPath(pathData) {
      const newPaths = Array.isArray(pathData) ? pathData : [pathData]
      this.$emit('update:pathData', [...this.pathData, ...newPaths])
    },

    removePath(index) {
      if (index >= 0 && index < this.pathData.length) {
        const newPaths = [...this.pathData]
        newPaths.splice(index, 1)
        this.$emit('update:pathData', newPaths)
      }
    },

    clearPaths() {
      this.$emit('update:pathData', [])
    },



    /**
     * 设置区域模式 (Set regional mode)
     * @param {Object} regionConfig - 区域配置对象 (Regional configuration object)
     */
    setRegionalMode(regionConfig) {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', true)
      this.$emit('update:regionConfig', { ...this.regionConfig, ...regionConfig })
      this.updateMapView()
    },

    /**
     * 切换到全球模式 (Switch to global mode)
     */
    setGlobalMode() {
      // 通过事件通知父组件更新区域模式 (Notify parent component to update regional mode via event)
      this.$emit('update:regionalMode', false)
      this.updateMapView()
    }
  }
}
</script>

<style scoped>
.standalone-world-map {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  border-radius: 8px;
  overflow: hidden;
}

.map-title {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.title-text {
  color: #00FFCC;
  font-size: 16px;
  font-weight: 500;
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
}

/* Mapbox GL JS 样式覆盖 (Mapbox GL JS style overrides) */
.map-container :deep(.mapboxgl-canvas-container) {
  background-color: #0F2E2C !important;
}

.map-container :deep(.mapboxgl-canvas) {
  background-color: #0F2E2C !important;
}

/* 隐藏Mapbox标志和属性 (Hide Mapbox logo and attribution) */
.map-container :deep(.mapboxgl-ctrl-logo) {
  display: none !important;
}

.map-container :deep(.mapboxgl-ctrl-attrib) {
  display: none !important;
}

/* 标记样式 (Marker styles) */
.map-container :deep(.marker) {
  transition: all 0.3s ease;
}

.map-container :deep(.marker:hover) {
  transform: scale(1.2);
}

/* 流动粒子样式 (Flow particle styles) */
.map-container :deep(.flow-particle) {
  transition: opacity 0.1s ease;
}

/* 箭头粒子样式 (Arrow particle styles) */
.map-container :deep(.arrow-particle) {
  transition: opacity 0.1s ease, transform 0.1s ease;
}

/* 网格覆盖层样式 - 仅在启用时显示 (Grid overlay styles - only show when enabled) */
.map-container.show-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 10;
}

/* 发光效果 (Glow effect) */
.map-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
  pointer-events: none;
  z-index: 11;
}
</style>
