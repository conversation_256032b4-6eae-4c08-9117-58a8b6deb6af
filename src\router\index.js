import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import(/* webpackChunkName: "home" */ '@/components/HomeView.vue')
  },
  {
    path: '/test',
    name: 'MapboxTest',
    component: () => import(/* webpackChunkName: "test" */ '@/components/MapboxTest.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
